Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFABB0, 0007FFFF9AB0) msys-2.0.dll+0x1FE8E
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210286019, 0007FFFFAA68, 0007FFFFABB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABB0  000210068E24 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE90  00021006A225 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF953C20000 ntdll.dll
7FF951B90000 KERNEL32.DLL
7FF9515B0000 KERNELBASE.dll
7FF951E10000 USER32.dll
7FF951070000 win32u.dll
000210040000 msys-2.0.dll
7FF952810000 GDI32.dll
7FF950D50000 gdi32full.dll
7FF951500000 msvcp_win.dll
7FF950F20000 ucrtbase.dll
7FF953120000 advapi32.dll
7FF951CA0000 msvcrt.dll
7FF951D60000 sechost.dll
7FF9531E0000 RPCRT4.dll
7FF950340000 CRYPTBASE.DLL
7FF9510A0000 bcryptPrimitives.dll
7FF951C60000 IMM32.DLL
