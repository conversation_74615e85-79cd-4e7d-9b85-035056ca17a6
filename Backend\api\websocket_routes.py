from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from services.analytics_websocket import analytics_ws_manager
from utils.logger import get_logger
import asyncio

logger = get_logger("websocket_routes")

router = APIRouter()

@router.websocket("/ws/analytics")
async def websocket_analytics(websocket: WebSocket):
    """WebSocket endpoint for real-time analytics streaming"""
    await analytics_ws_manager.connect(websocket)
    logger.info("Analytics WebSocket client connected")

    try:
        # Send initial data
        await analytics_ws_manager.send_initial_data(websocket)

        # Keep connection alive - the broadcasting is handled by the manager
        # We just need to keep the connection open
        while True:
            try:
                # Wait for messages from client with timeout
                # This allows the connection to stay alive while handling optional client messages
                message = await asyncio.wait_for(websocket.receive_json(), timeout=1.0)
                await analytics_ws_manager.handle_client_message(websocket, message)

            except asyncio.TimeoutError:
                # No message received, continue to keep connection alive
                continue
            except WebSocketDisconnect:
                logger.info("Analytics WebSocket client disconnected")
                break
            except Exception as e:
                logger.debug(f"WebSocket receive error (normal): {e}")
                # Continue to keep connection alive even if there are receive errors
                await asyncio.sleep(1)

    except WebSocketDisconnect:
        logger.info("Analytics WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Unexpected error in WebSocket analytics: {e}")
    finally:
        analytics_ws_manager.disconnect(websocket)
