import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import snowflake.connector
from utils.snowflake_setup import get_snowflake_config
from utils.logger import get_logger

logger = get_logger("analytics_service")


class AnalyticsService:
    """Service for querying and aggregating API usage analytics"""

    def __init__(self):
        self.snowflake_config = get_snowflake_config()

    def get_connection(self):
        """Get a Snowflake connection with timeout"""
        try:
            # Add connection timeout to prevent hanging
            config = self.snowflake_config.copy()
            config['connect_timeout'] = 10  # 10 second timeout
            config['network_timeout'] = 10  # 10 second network timeout
            return snowflake.connector.connect(**config)
        except Exception as e:
            logger.error(f"Failed to connect to Snowflake: {e}")
            raise

    def get_dashboard_metrics(self) -> Dict[str, Any]:
        """Get overall dashboard metrics"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Get metrics for the last 30 days - using correct table structure
            cursor.execute("""
                SELECT
                    COUNT(*) as total_calls,
                    AVG(response_time_ms) as avg_response_time,
                    SUM(estimated_cost) as total_cost,
                    SUM(tokens_total) as tokens_used,
                    CASE
                        WHEN COUNT(*) = 0 THEN 0
                        ELSE COUNT(CASE WHEN status_code < 400 THEN 1 END) * 100.0 / COUNT(*)
                    END as success_rate,
                    COUNT(DISTINCT session_id) as active_sessions
                FROM api_usage_metrics
                WHERE request_timestamp >= DATEADD(day, -30, CURRENT_TIMESTAMP())
            """)

            result = cursor.fetchone()

            if result:
                return {
                    'totalCalls': int(result[0]) if result[0] else 0,
                    # Convert to seconds
                    'avgResponseTime': round(float(result[1]) / 1000, 2) if result[1] else 0,
                    'totalCost': round(float(result[2]), 4) if result[2] else 0,
                    'tokensUsed': int(result[3]) if result[3] else 0,
                    'successRate': round(float(result[4]), 1) if result[4] else 0,
                    'activeProjects': int(result[5]) if result[5] else 0
                }

            return {
                'totalCalls': 0, 'avgResponseTime': 0, 'totalCost': 0,
                'tokensUsed': 0, 'successRate': 0, 'activeProjects': 0
            }

        except Exception as e:
            logger.error(f"Error getting dashboard metrics: {e}")
            return {
                'totalCalls': 0, 'avgResponseTime': 0, 'totalCost': 0,
                'tokensUsed': 0, 'successRate': 0, 'activeProjects': 0
            }
        finally:
            if conn:
                conn.close()

    def get_usage_series(self, time_range: str = '30D') -> Dict[str, List[Dict[str, Any]]]:
        """Get usage time series data for different time ranges"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Define time ranges
            time_ranges = {
                '7D': 7,
                '30D': 30,
                '90D': 90,
                '1Y': 365
            }

            result = {}

            for range_key, days in time_ranges.items():
                # Get daily aggregated data - using correct table structure
                cursor.execute("""
                    SELECT
                        DATE(request_timestamp) as date,
                        COUNT(*) as calls,
                        SUM(estimated_cost) as cost,
                        SUM(tokens_total) as tokens,
                        AVG(response_time_ms) as avg_response_time
                    FROM api_usage_metrics
                    WHERE request_timestamp >= DATEADD(day, -%s, CURRENT_TIMESTAMP())
                    GROUP BY DATE(request_timestamp)
                    ORDER BY date
                """, (days,))

                rows = cursor.fetchall()

                series_data = []
                for row in rows:
                    series_data.append({
                        'date': row[0].strftime('%Y-%m-%d') if row[0] else '',
                        'calls': int(row[1]) if row[1] else 0,
                        'cost': round(float(row[2]), 4) if row[2] else 0,
                        'tokens': int(row[3]) if row[3] else 0,
                        'avgResponseTime': round(float(row[4]) / 1000, 2) if row[4] else 0
                    })

                result[range_key] = series_data

            return result

        except Exception as e:
            logger.error(f"Error getting usage series: {e}")
            return {'7D': [], '30D': [], '90D': [], '1Y': []}
        finally:
            if conn:
                conn.close()

    def get_model_distribution(self) -> List[Dict[str, Any]]:
        """Get model usage distribution"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    COALESCE(model_used, 'Unknown') as model,
                    COUNT(*) as usage_count,
                    SUM(estimated_cost) as total_cost,
                    SUM(tokens_total) as total_tokens
                FROM api_usage_metrics
                WHERE request_timestamp >= DATEADD(day, -30, CURRENT_TIMESTAMP())
                GROUP BY model_used
                ORDER BY usage_count DESC
            """)

            rows = cursor.fetchall()

            distribution = []
            for row in rows:
                distribution.append({
                    'name': row[0] if row[0] else 'Unknown',
                    'value': int(row[1]) if row[1] else 0,
                    'cost': round(float(row[2]), 4) if row[2] else 0,
                    'tokens': int(row[3]) if row[3] else 0
                })

            return distribution

        except Exception as e:
            logger.error(f"Error getting model distribution: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def get_custom_usage_data(self, days: int) -> List[Dict[str, Any]]:
        """Get usage data for custom number of days"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    DATE(request_timestamp) as date,
                    COUNT(*) as calls,
                    SUM(estimated_cost) as cost,
                    SUM(tokens_total) as tokens,
                    AVG(response_time_ms) as avg_response_time
                FROM api_usage_metrics
                WHERE request_timestamp >= DATEADD(day, -%s, CURRENT_TIMESTAMP())
                GROUP BY DATE(request_timestamp)
                ORDER BY date
            """, (days,))

            rows = cursor.fetchall()

            series_data = []
            for row in rows:
                series_data.append({
                    'date': row[0].strftime('%Y-%m-%d') if row[0] else '',
                    'calls': int(row[1]) if row[1] else 0,
                    'cost': round(float(row[2]), 4) if row[2] else 0,
                    'tokens': int(row[3]) if row[3] else 0,
                    'avgResponseTime': round(float(row[4]) / 1000, 2) if row[4] else 0
                })

            return series_data

        except Exception as e:
            logger.error(f"Error getting custom usage data: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def get_endpoint_stats(self) -> List[Dict[str, Any]]:
        """Get statistics by endpoint"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    COALESCE(endpoint, 'Unknown') as endpoint,
                    COUNT(*) as total_calls,
                    AVG(response_time_ms) as avg_response_time,
                    SUM(estimated_cost) as total_cost,
                    CASE
                        WHEN COUNT(*) = 0 THEN 0
                        ELSE COUNT(CASE WHEN status_code < 400 THEN 1 END) * 100.0 / COUNT(*)
                    END as success_rate
                FROM api_usage_metrics
                WHERE request_timestamp >= DATEADD(day, -30, CURRENT_TIMESTAMP())
                GROUP BY endpoint
                ORDER BY total_calls DESC
            """)

            rows = cursor.fetchall()

            stats = []
            for row in rows:
                stats.append({
                    'endpoint': row[0],
                    'totalCalls': int(row[1]) if row[1] else 0,
                    'avgResponseTime': round(float(row[2]) / 1000, 2) if row[2] else 0,
                    'totalCost': round(float(row[3]), 4) if row[3] else 0,
                    'successRate': round(float(row[4]), 1) if row[4] else 0
                })

            return stats

        except Exception as e:
            logger.error(f"Error getting endpoint stats: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time metrics for WebSocket streaming"""
        try:
            logger.info("Getting real-time metrics...")

            # Get dashboard metrics
            logger.info("Fetching dashboard metrics...")
            metrics = self.get_dashboard_metrics()
            logger.info(f"Dashboard metrics: {metrics}")

            # Get model distribution
            logger.info("Fetching model distribution...")
            model_distribution = self.get_model_distribution()
            logger.info(f"Model distribution: {model_distribution}")

            # Get usage series for all time ranges
            logger.info("Fetching usage series...")
            usage_series = self.get_usage_series()
            logger.info(f"Usage series: {usage_series}")

            result = {
                'metrics': metrics,
                'modelDistribution': model_distribution,
                'usageSeries': usage_series,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Real-time metrics result: {result}")
            return result

        except Exception as e:
            logger.error(f"Error getting real-time metrics: {e}", exc_info=True)
            return {
                'metrics': {'totalCalls': 0, 'avgResponseTime': 0, 'totalCost': 0, 'tokensUsed': 0, 'successRate': 0, 'activeProjects': 0},
                'modelDistribution': [],
                'usageSeries': {'7D': [], '30D': [], '90D': [], '1Y': []},
                'timestamp': datetime.now().isoformat()
            }
