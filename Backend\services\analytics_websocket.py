import asyncio
from typing import Set
from fastapi import WebSocket
from services.analytics_service import AnalyticsService
from utils.logger import get_logger

logger = get_logger("analytics_websocket")

class AnalyticsWebSocketManager:
    """Enhanced WebSocket manager specifically for analytics streaming"""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.analytics_service = AnalyticsService()
        self.broadcast_task = None
        self.is_broadcasting = False
    
    async def connect(self, websocket: WebSocket):
        """Connect a new WebSocket client"""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"Analytics WebSocket client connected. Total: {len(self.active_connections)}")
        
        # Start broadcasting if this is the first connection
        if len(self.active_connections) == 1 and not self.is_broadcasting:
            await self.start_broadcasting()
    
    def disconnect(self, websocket: WebSocket):
        """Disconnect a WebSocket client"""
        self.active_connections.discard(websocket)
        logger.info(f"Analytics WebSocket client disconnected. Total: {len(self.active_connections)}")
        
        # Stop broadcasting if no connections remain
        if len(self.active_connections) == 0 and self.is_broadcasting:
            self.stop_broadcasting()
    
    async def send_to_client(self, websocket: WebSocket, data: dict):
        """Send data to a specific client"""
        try:
            await websocket.send_json(data)
        except Exception as e:
            logger.error(f"Failed to send data to client: {e}")
            self.disconnect(websocket)
    
    async def broadcast_analytics(self, data: dict):
        """Broadcast analytics data to all connected clients"""
        if not self.active_connections:
            return
        
        disconnected_clients = set()
        
        for websocket in self.active_connections.copy():
            try:
                await websocket.send_json(data)
            except Exception as e:
                logger.error(f"Failed to broadcast to client: {e}")
                disconnected_clients.add(websocket)
        
        # Remove disconnected clients
        for websocket in disconnected_clients:
            self.disconnect(websocket)
    
    async def start_broadcasting(self):
        """Start the background broadcasting task"""
        if self.is_broadcasting:
            return
        
        self.is_broadcasting = True
        self.broadcast_task = asyncio.create_task(self._broadcast_loop())
        logger.info("Started analytics broadcasting")
    
    def stop_broadcasting(self):
        """Stop the background broadcasting task"""
        if not self.is_broadcasting:
            return
        
        self.is_broadcasting = False
        if self.broadcast_task:
            self.broadcast_task.cancel()
        logger.info("Stopped analytics broadcasting")
    
    async def _broadcast_loop(self):
        """Background loop for broadcasting analytics updates"""
        while self.is_broadcasting and self.active_connections:
            try:
                # Get fresh analytics data
                analytics_data = self.analytics_service.get_real_time_metrics()
                
                # Broadcast to all connected clients
                await self.broadcast_analytics(analytics_data)
                
                logger.debug(f"Broadcasted analytics to {len(self.active_connections)} clients")
                
                # Wait 30 seconds before next broadcast
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                logger.info("Analytics broadcast loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in analytics broadcast loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(10)
        
        self.is_broadcasting = False
    
    async def send_initial_data(self, websocket: WebSocket):
        """Send initial analytics data to a newly connected client"""
        try:
            initial_data = self.analytics_service.get_real_time_metrics()
            await self.send_to_client(websocket, initial_data)
            logger.debug("Sent initial analytics data to new client")
        except Exception as e:
            logger.error(f"Failed to send initial data: {e}")
    
    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.active_connections)
    
    async def handle_client_message(self, websocket: WebSocket, message: dict):
        """Handle messages from clients (if needed for interactive features)"""
        try:
            message_type = message.get('type')
            
            if message_type == 'ping':
                await self.send_to_client(websocket, {'type': 'pong'})
            elif message_type == 'request_update':
                # Send immediate update
                data = self.analytics_service.get_real_time_metrics()
                await self.send_to_client(websocket, data)
            elif message_type == 'custom_query':
                # Handle custom analytics queries
                days = message.get('days', 30)
                custom_data = self.analytics_service.get_custom_usage_data(days)
                await self.send_to_client(websocket, {
                    'type': 'custom_data',
                    'data': custom_data
                })
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling client message: {e}")

# Global instance
analytics_ws_manager = AnalyticsWebSocketManager()
