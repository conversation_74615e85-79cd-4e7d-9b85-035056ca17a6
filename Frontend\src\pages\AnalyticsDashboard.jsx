import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";

const AnalyticsDashboard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("30D");
  const [customDays, setCustomDays] = useState("");
  const [isCustomMode, setIsCustomMode] = useState(false);
  const [customUsageData, setCustomUsageData] = useState([]);
  const [isLoadingCustom, setIsLoadingCustom] = useState(false);
  const [customError, setCustomError] = useState("");
  const [animateStats, setAnimateStats] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);

  // Dashboard metrics state
  const [metrics, setMetrics] = useState({
    totalCalls: 0,
    totalCost: 0,
    tokensUsed: 0,
    successRate: 0,
    avgResponseTime: 0,
    activeProjects: 0,
  });

  // Live data from backend
  const [usageSeries, setUsageSeries] = useState({
    "7D": [],
    "30D": [],
    "90D": [],
    "1Y": [],
  });
  const [modelDistribution, setModelDistribution] = useState([]);

  // Friendly display names for model codes
  const toDisplayModelName = useCallback((code) => {
    const name = String(code || "").toLowerCase();
    if (name === "gpt-5" || name.startsWith("gpt-5")) return "GPT-5";
    if (name.startsWith("gpt-4-32k")) return "GPT-4 32K";
    if (name.startsWith("gpt-4-0613") || name === "gpt-4") return "GPT-4";
    if (name.startsWith("gpt-4o-mini")) return "GPT-4o Mini";
    if (name.startsWith("gpt-4o")) return "GPT-4o";
    if (name.startsWith("gpt-3.5-turbo")) return "GPT-3.5 Turbo";
    if (name.includes("davinci")) return "GPT-3.0 (Davinci)";
    if (name.includes("curie")) return "Curie";
    if (name.includes("babbage")) return "Babbage";
    if (name.includes("ada")) return "Ada";
    if (name.includes("3.5") && name.includes("mini")) return "GPT-3.5 Mini";
    if (name.includes("3.5") && name.includes("nano")) return "GPT-3.5 Nano";
    if (
      (name.includes("3.0") || name.includes("gpt-3")) &&
      (name.includes("tiny") || name.includes("nano"))
    )
      return "GPT-3.0 Tiny/Nano";
    return code || "Unknown Model";
  }, []);

  // X-axis formatter
  const formatXAxisTick = useCallback((value) => String(value || ""), []);

  // Format number function
  const formatNumber = useCallback((num) => {
    if (num >= 1e6) return (num / 1e6).toFixed(1) + "M";
    if (num >= 1e3) return (num / 1e3).toFixed(1) + "K";
    return Number(num || 0).toLocaleString();
  }, []);

  // Currency formatter
  const formatCurrency = useCallback((amount) => {
    const num = Number(amount || 0);
    if (num >= 1) return `$${num.toFixed(2)}`;
    if (num >= 0.01) return `$${num.toFixed(2)}`;
    if (num >= 0.0001) return `$${num.toFixed(4)}`;
    return `$${num.toFixed(6)}`;
  }, []);

  // Fetch custom usage data
  const fetchCustomUsage = useCallback(async (days) => {
    setIsLoadingCustom(true);
    setCustomError("");

    try {
      // Use the backend URL from environment or fallback to localhost:8077
      const baseUrl =
        import.meta.env.VITE_BACKEND_URL || "http://127.0.0.1:8077";
      const url = `${baseUrl}/api/analytics/usage?custom_days=${days}`;

      console.log("Fetching custom usage data:", { days, url, baseUrl });

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Custom usage data received:", data);
      setCustomUsageData(data.series || []);
    } catch (error) {
      console.error("Failed to fetch custom usage data:", error);
      setCustomError(error.message || "Failed to load custom data");
      setCustomUsageData([]);
    } finally {
      setIsLoadingCustom(false);
    }
  }, []);

  // Handle custom days input
  const handleCustomDaysSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      const days = parseInt(customDays);

      console.log("Custom days submit:", {
        customDays,
        days,
        isNaN: isNaN(days),
      });

      if (isNaN(days) || days < 1 || days > 3650) {
        setCustomError("Please enter a valid number between 1 and 3650 days");
        return;
      }

      setIsCustomMode(true);
      await fetchCustomUsage(days);
    },
    [customDays, fetchCustomUsage]
  );

  // Handle preset time range selection
  const handleTimeRangeChange = useCallback((range) => {
    setSelectedTimeRange(range);
    setIsCustomMode(false);
    setCustomError("");
    setCustomUsageData([]);
  }, []);

  // Reset to preset mode
  const resetToPresetMode = useCallback(() => {
    setIsCustomMode(false);
    setCustomDays("");
    setCustomError("");
    setCustomUsageData([]);
    setSelectedTimeRange("30D");
  }, []);

  // WebSocket connection
  const connect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    try {
      // Use the backend URL from environment or fallback to localhost:8077
      const backendUrl =
        import.meta.env.VITE_BACKEND_URL || "http://127.0.0.1:8077";
      // Determine WebSocket protocol based on backend URL, not window location
      const protocol = backendUrl.startsWith("https:") ? "wss:" : "ws:";
      const host = backendUrl.replace(/^https?:\/\//, "");
      const wsUrl = `${protocol}//${host}/ws/analytics`;

      console.log("Connecting to WebSocket:", { backendUrl, host, wsUrl });

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log("WebSocket connected");
        setIsConnected(true);
      };

      wsRef.current.onclose = (event) => {
        console.log("WebSocket closed:", event.code, event.reason);
        setIsConnected(false);

        const delay = Math.min(1000 * Math.pow(2, Math.random()), 30000);
        reconnectTimeoutRef.current = setTimeout(connect, delay);
      };

      wsRef.current.onerror = (error) => {
        console.error("WebSocket error:", error);
        setIsConnected(false);
      };

      wsRef.current.onmessage = (e) => {
        try {
          const data = JSON.parse(e.data);
          if (!data) return;

          // Handle the data structure sent by the backend
          if (data.metrics) {
            setMetrics((prev) => {
              const newMetrics = {
                totalCalls: data.metrics.totalCalls ?? prev.totalCalls,
                totalCost: data.metrics.totalCost ?? prev.totalCost,
                tokensUsed: data.metrics.tokensUsed ?? prev.tokensUsed,
                successRate: data.metrics.successRate ?? prev.successRate,
                avgResponseTime:
                  data.metrics.avgResponseTime ?? prev.avgResponseTime,
                activeProjects:
                  data.metrics.activeProjects ?? prev.activeProjects,
              };

              if (JSON.stringify(newMetrics) !== JSON.stringify(prev)) {
                return newMetrics;
              }
              return prev;
            });
          }

          if (data.usageSeries) {
            setUsageSeries(data.usageSeries);
          }

          if (Array.isArray(data.modelDistribution)) {
            const mapped = data.modelDistribution.map((m) => ({
              name: toDisplayModelName(m?.name),
              value: m?.value ?? 0,
            }));
            setModelDistribution(mapped);
          }

          console.log("Analytics data updated:", data.timestamp);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };
    } catch (error) {
      console.error("Failed to create WebSocket:", error);
      setIsConnected(false);
      reconnectTimeoutRef.current = setTimeout(connect, 5000);
    }
  }, []);

  useEffect(() => {
    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connect]);

  useEffect(() => {
    const timer = setTimeout(() => setAnimateStats(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Get current chart data based on mode
  const currentChartData = useMemo(() => {
    if (isCustomMode) {
      return customUsageData;
    }
    return usageSeries[selectedTimeRange] || [];
  }, [isCustomMode, customUsageData, usageSeries, selectedTimeRange]);

  // Get current chart title
  const currentChartTitle = useMemo(() => {
    if (isCustomMode) {
      return `API Usage - Last ${customDays} Days`;
    }
    return "API Usage Over Time";
  }, [isCustomMode, customDays]);

  // Stats data
  const statsData = useMemo(
    () => [
      {
        title: "Total API Calls",
        value: formatNumber(metrics.totalCalls),
        change: "+23.5%",
        positive: true,
        icon: "🔥",
      },
      {
        title: "Total Cost",
        value: formatCurrency(metrics.totalCost),
        change: "+12.3%",
        positive: true,
        icon: "💰",
      },
      {
        title: "Tokens Used",
        value: formatNumber(metrics.tokensUsed),
        change: "+18.7%",
        positive: true,
        icon: "🎯",
      },
      {
        title: "Success Rate",
        value: `${metrics.successRate}%`,
        change: "-0.3%",
        positive: false,
        icon: "✅",
      },
      {
        title: "Avg Response Time",
        value: `${metrics.avgResponseTime}s`,
        change: "15% faster",
        positive: true,
        icon: "⚡",
      },
      {
        title: "Active Projects",
        value: String(metrics.activeProjects || 0),
        change: "+2 new",
        positive: true,
        icon: "📱",
      },
    ],
    [metrics, formatNumber, formatCurrency]
  );

  // Memoized components
  const ConnectionStatus = React.memo(() => (
    <div
      className={`flex items-center gap-2 text-xs ${
        isConnected ? "text-green-400" : "text-red-400"
      }`}
    >
      <div
        className={`w-2 h-2 rounded-full ${
          isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
        }`}
      ></div>
      <span>{isConnected ? "Live Updates Active" : "Reconnecting..."}</span>
    </div>
  ));

  const StatCard = React.memo(({ stat, index }) => (
    <div
      className={`bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-600 rounded-2xl p-6 relative overflow-hidden ${
        animateStats ? "animate-fade-in" : "opacity-0"
      }`}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-500"></div>
      <div className="flex justify-between items-start mb-4">
        <div className="text-sm text-slate-400 font-medium">{stat.title}</div>
        <div className="text-2xl p-2 bg-indigo-500/10 rounded-lg">
          {stat.icon}
        </div>
      </div>
      <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
      <div
        className={`flex items-center gap-1 text-xs ${
          stat.positive ? "text-green-400" : "text-red-400"
        }`}
      >
        <span>{stat.positive ? "↗" : "↘"}</span>
        <span>{stat.change} from last month</span>
      </div>
    </div>
  ));

  const TimeSelector = React.memo(() => (
    <div className="flex flex-col gap-3">
      {/* Preset Time Ranges */}
      <div className="flex gap-2 flex-wrap">
        {["7D", "30D", "90D", "1Y"].map((r) => (
          <button
            key={r}
            onClick={() => handleTimeRangeChange(r)}
            disabled={isLoadingCustom}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              selectedTimeRange === r && !isCustomMode
                ? "bg-indigo-500 text-white"
                : "border border-slate-600 text-slate-400 hover:text-white disabled:opacity-50"
            }`}
          >
            {r}
          </button>
        ))}
      </div>

      {/* Custom Days Input */}
      <div className="flex items-center gap-2">
        <form
          onSubmit={handleCustomDaysSubmit}
          className="flex items-center gap-2"
        >
          <input
            type="number"
            min="1"
            max="3650"
            placeholder="Custom days"
            value={customDays}
            onChange={(e) => setCustomDays(e.target.value)}
            disabled={isLoadingCustom}
            className="px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 w-28 disabled:opacity-50"
          />
          <button
            type="submit"
            disabled={isLoadingCustom || !customDays.trim()}
            className="px-3 py-1 text-sm bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:opacity-50 text-white rounded-md transition-colors"
          >
            {isLoadingCustom ? "..." : "Go"}
          </button>
        </form>

        {isCustomMode && (
          <button
            onClick={resetToPresetMode}
            className="px-2 py-1 text-xs text-slate-400 hover:text-white border border-slate-600 rounded-md transition-colors"
          >
            Reset
          </button>
        )}
      </div>

      {/* Loading Indicator */}
      {isLoadingCustom && (
        <div className="text-xs text-blue-400 bg-blue-900/20 border border-blue-800 rounded-md p-2">
          Loading custom data...
        </div>
      )}

      {/* Custom Error Display */}
      {customError && (
        <div className="text-xs text-red-400 bg-red-900/20 border border-red-800 rounded-md p-2">
          {customError}
        </div>
      )}

      {/* Custom Mode Indicator */}
      {isCustomMode && !customError && !isLoadingCustom && (
        <div className="text-xs text-green-400 bg-green-900/20 border border-green-800 rounded-md p-2">
          Showing custom {customDays} days • {currentChartData.length} data
          points
        </div>
      )}
    </div>
  ));

  const UsageChart = ({ data, title }) => (
    <div className="h-64 relative">
      {isLoadingCustom && isCustomMode && (
        <div className="absolute inset-0 bg-slate-800/80 flex items-center justify-center z-10">
          <div className="flex items-center gap-2 text-slate-400">
            <div className="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin"></div>
            <span>Loading custom data...</span>
          </div>
        </div>
      )}
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data || []}>
          <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
          <XAxis
            dataKey="date"
            stroke="#64748b"
            fontSize={12}
            tickFormatter={formatXAxisTick}
          />
          <YAxis stroke="#64748b" fontSize={12} />
          <Tooltip
            contentStyle={{
              background: "#1e293b",
              border: "1px solid #334155",
            }}
          />
          <Line
            type="monotone"
            dataKey="calls"
            stroke="#6366f1"
            strokeWidth={2}
            dot={{ r: 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );

  const ModelChart = ({ data }) => (
    <div className="h-48">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data || []}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={70}
            dataKey="value"
          >
            {(data || []).map((entry, i) => {
              const colors = [
                "#6366f1",
                "#8b5cf6",
                "#06b6d4",
                "#f59e0b",
                "#10b981",
                "#ef4444",
                "#14b8a6",
                "#a78bfa",
              ];
              return <Cell key={i} fill={colors[i % colors.length]} />;
            })}
          </Pie>
          <Tooltip
            contentStyle={{
              background: "#1e293b",
              border: "1px solid #334155",
            }}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );

  return (
    <div className="flex h-screen bg-slate-900 text-white overflow-hidden">
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex-shrink-0 p-6 border-b border-slate-700 bg-slate-900">
          <div className="flex justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-1">Analytics Dashboard</h1>
              <p className="text-slate-400 text-sm">
                Monitor your AI usage, costs, and performance metrics
              </p>
            </div>
            <ConnectionStatus />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Charts */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 mb-6">
            {/* Usage Over Time */}
            <div className="xl:col-span-2 bg-slate-800 border border-slate-700 rounded-xl p-4">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold">{currentChartTitle}</h3>
                <TimeSelector />
              </div>
              <UsageChart data={currentChartData} title={currentChartTitle} />
            </div>

            {/* Model Distribution */}
            <div className="bg-slate-800 border border-slate-700 rounded-xl p-4">
              <h3 className="text-lg font-semibold mb-4">Model Distribution</h3>
              <ModelChart data={modelDistribution} />
              <div className="mt-3 space-y-2">
                {(() => {
                  const total = (modelDistribution || []).reduce(
                    (acc, cur) => acc + (cur.value || 0),
                    0
                  );
                  const colors = [
                    "#6366f1",
                    "#8b5cf6",
                    "#06b6d4",
                    "#f59e0b",
                    "#10b981",
                    "#ef4444",
                    "#14b8a6",
                    "#a78bfa",
                  ];
                  return (modelDistribution || []).map((m, i) => {
                    const pct =
                      total > 0 ? ((m.value / total) * 100).toFixed(1) : "0.0";
                    return (
                      <div key={i} className="flex items-center gap-2">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ background: colors[i % colors.length] }}
                        ></div>
                        <span className="text-xs text-slate-300">{m.name}</span>
                        <span className="text-xs text-slate-400 ml-auto">
                          {pct}% • {m.value}
                        </span>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mb-6">
            {statsData.map((s, i) => (
              <StatCard key={s.title} stat={s} index={i} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
