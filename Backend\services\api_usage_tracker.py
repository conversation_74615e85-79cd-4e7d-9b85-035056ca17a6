import time
import uuid
import json
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import snowflake.connector
from utils.snowflake_setup import get_snowflake_config
from utils.logger import get_logger

logger = get_logger("api_usage_tracker")


class APIUsageTracker:
    """Service for tracking API usage metrics"""

    def __init__(self):
        self.snowflake_config = get_snowflake_config()
        self._connection_pool = {}

    def get_connection(self):
        """Get a Snowflake connection"""
        try:
            return snowflake.connector.connect(**self.snowflake_config)
        except Exception as e:
            logger.error(f"Failed to connect to Snowflake: {e}")
            raise

    async def log_api_usage(self, usage_data: Dict[str, Any]):
        """Log API usage data to Snowflake"""
        try:
            # Run database operation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._insert_usage_data, usage_data)
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")

    def _insert_usage_data(self, usage_data: Dict[str, Any]):
        """Insert usage data into Snowflake (blocking operation)"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO api_usage_metrics (
                    usage_id, endpoint, method, status_code, response_time_ms,
                    request_timestamp, session_id, user_ip, user_agent,
                    model_used, tokens_input, tokens_output, tokens_total,
                    estimated_cost, request_size_bytes, response_size_bytes,
                    error_message, additional_metadata
                ) VALUES (
                    %(usage_id)s, %(endpoint)s, %(method)s, %(status_code)s, %(response_time_ms)s,
                    %(request_timestamp)s, %(session_id)s, %(user_ip)s, %(user_agent)s,
                    %(model_used)s, %(tokens_input)s, %(tokens_output)s, %(tokens_total)s,
                    %(estimated_cost)s, %(request_size_bytes)s, %(response_size_bytes)s,
                    %(error_message)s, %(additional_metadata)s
                )
            """, usage_data)

            conn.commit()
            logger.debug(f"Logged API usage for {usage_data['endpoint']}")

        except Exception as e:
            logger.error(f"Database error logging API usage: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()


class APIUsageMiddleware(BaseHTTPMiddleware):
    """Middleware to track API usage metrics"""

    def __init__(self, app, tracker: APIUsageTracker):
        super().__init__(app)
        self.tracker = tracker

        # Endpoints to track
        self.tracked_endpoints = {
            '/api/chat', '/api/upload-document', '/transcribe',
            '/api/tts', '/api/sessions', '/api/session-info'
        }

        # Model cost estimation (tokens per dollar)
        self.model_costs = {
            'gpt-4': {'input': 0.03/1000, 'output': 0.06/1000},
            'gpt-4o': {'input': 0.005/1000, 'output': 0.015/1000},
            'gpt-4o-mini': {'input': 0.00015/1000, 'output': 0.0006/1000},
            'gpt-3.5-turbo': {'input': 0.001/1000, 'output': 0.002/1000},
        }

    async def dispatch(self, request: Request, call_next):
        # Skip non-API endpoints
        if not any(request.url.path.startswith(endpoint) for endpoint in self.tracked_endpoints):
            return await call_next(request)

        # Start timing and capture request timestamp
        start_time = time.time()
        request_timestamp = datetime.now()
        usage_id = str(uuid.uuid4())

        # Get request info
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get('user-agent', '')
        session_id = self._extract_session_id(request)

        # Extract model info from request body before processing
        model_used = None
        request_body = None
        request_data = None
        try:
            request_body = await request.body()
            if request_body and request.url.path == '/api/chat':
                request_data = json.loads(request_body)
                # Try different possible field names for model
                model_used = (request_data.get('model') or
                              request_data.get('model_name') or
                              request_data.get('llm_model'))
                logger.debug(
                    f"Extracted model: {model_used} from request data: {list(request_data.keys())}")
        except Exception as e:
            logger.debug(f"Could not parse request body: {e}")

        # Get request size
        request_size = len(request_body) if request_body else 0

        response = None
        error_message = None
        tokens_input = 0
        tokens_output = 0

        try:
            # Process request
            response = await call_next(request)

            # Extract token info from request and response
            try:
                tokens_input, tokens_output = await self._extract_token_info(request_data, response)
            except Exception as e:
                logger.debug(f"Could not extract token info: {e}")

        except Exception as e:
            error_message = str(e)
            logger.error(f"Error processing request: {e}")
            # Create error response
            response = StarletteResponse(
                content=json.dumps({"error": "Internal server error"}),
                status_code=500,
                media_type="application/json"
            )

        # Calculate response time
        response_time_ms = (time.time() - start_time) * 1000

        # Get response size
        response_size = 0
        if hasattr(response, 'body'):
            response_size = len(response.body) if response.body else 0

        # Calculate estimated cost
        estimated_cost = self._calculate_cost(
            model_used, tokens_input, tokens_output)

        # Prepare usage data
        usage_data = {
            'usage_id': usage_id,
            'endpoint': request.url.path,
            'method': request.method,
            'status_code': response.status_code if response else 500,
            'response_time_ms': response_time_ms,
            'request_timestamp': request_timestamp,  # Store actual API call time
            'session_id': session_id,
            'user_ip': client_ip,
            'user_agent': user_agent,
            'model_used': model_used,
            'tokens_input': tokens_input,
            'tokens_output': tokens_output,
            'tokens_total': tokens_input + tokens_output,
            'estimated_cost': estimated_cost,
            'request_size_bytes': request_size,
            'response_size_bytes': response_size,
            'error_message': error_message,
            'additional_metadata': json.dumps({
                'query_params': dict(request.query_params),
                'path_params': dict(request.path_params) if hasattr(request, 'path_params') else {}
            })
        }

        # Log usage asynchronously
        asyncio.create_task(self.tracker.log_api_usage(usage_data))

        return response

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()

        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip

        # Fallback to client info
        if request.client:
            return request.client.host

        return 'unknown'

    def _extract_session_id(self, request: Request) -> Optional[str]:
        """Extract session ID from request"""
        # Try to get from headers
        session_id = request.headers.get('x-session-id')
        if session_id:
            return session_id

        # Try to get from query params
        session_id = request.query_params.get('session_id')
        if session_id:
            return session_id

        # Try to get from cookies (if session middleware is used)
        if hasattr(request, 'cookies'):
            session_id = request.cookies.get('session')
            if session_id:
                return session_id

        # Always generate a session identifier - never return None
        # This ensures we always have a session_id for analytics
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get('user-agent', '')

        # Create a simple hash-based session identifier
        import hashlib
        # Limit user agent length
        session_data = f"{client_ip}:{user_agent[:50]}"
        session_hash = hashlib.md5(session_data.encode()).hexdigest()[:16]
        return f"auto_{session_hash}"

    async def _extract_token_info(self, request_data: dict, response: Response) -> tuple:
        """Extract token information from request and response"""
        tokens_input = 0
        tokens_output = 0

        try:
            # Estimate input tokens from request
            if request_data:
                # Count tokens in user message and system prompt
                user_message = request_data.get('user_message', '')
                system_prompt = request_data.get('system_prompt', '')

                # Rough estimation: ~4 characters per token
                input_text = f"{system_prompt} {user_message}"
                tokens_input = max(1, len(input_text) // 4)
                logger.debug(
                    f"Estimated input tokens: {tokens_input} from text length: {len(input_text)}")

            # For response tokens, we need to read the response body
            # But FastAPI responses are consumed, so we'll use a different approach
            # We'll estimate based on a reasonable assumption for now
            if tokens_input > 0:
                # Estimate output tokens as roughly 50-150% of input tokens for typical responses
                tokens_output = max(10, int(tokens_input * 1.2))
                logger.debug(
                    f"Estimated output tokens: {tokens_output} based on input: {tokens_input}")
            else:
                # Fallback estimation
                tokens_output = 50  # Reasonable default for typical responses

        except Exception as e:
            logger.error(f"Error extracting token info: {e}")
            # Provide reasonable defaults instead of 0
            tokens_input = 20  # Reasonable default for typical questions
            tokens_output = 50  # Reasonable default for typical responses

        return tokens_input, tokens_output

    def _calculate_cost(self, model: Optional[str], tokens_input: int, tokens_output: int) -> float:
        """Calculate estimated cost based on model and tokens"""
        if not model or model not in self.model_costs:
            return 0.0

        costs = self.model_costs[model]
        input_cost = tokens_input * costs['input']
        output_cost = tokens_output * costs['output']

        return input_cost + output_cost
